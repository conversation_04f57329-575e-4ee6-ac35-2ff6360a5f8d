﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\buf.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\c14n.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\catalog.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\chvalid.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\debugXML.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\dict.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\encoding.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\entities.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\error.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\globals.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\hash.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\HTMLparser.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\HTMLtree.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\lintmain.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\list.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\nanohttp.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\parser.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\parserInternals.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\pattern.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\relaxng.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\SAX2.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\schematron.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\shell.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\threads.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\tree.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\uri.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\valid.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xinclude.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xlink.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlcatalog.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlIO.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmllint.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlmemory.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlmodule.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlreader.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlregexp.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlsave.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlschemas.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlschemastypes.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlstring.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlunicode.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xmlwriter.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xpath.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xpointer.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="src\xzlib.c">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="src\html5ent.inc">
      <Filter>头文件</Filter>
    </None>
    <None Include="src\iso8859x.inc">
      <Filter>头文件</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\libxml.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="src\timsort.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
</Project>