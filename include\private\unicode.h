#ifndef XML_UNICODE_H_PRIVATE__
#define XML_UNICODE_H_PRIVATE__

XML_HIDDEN int xmlUCSIsBlock	(int code, const char *block);
XML_HIDDEN int xmlUCSIsCat	(int code, const char *cat);

XML_HIDDEN int xmlUCSIsCatC	(int code);
XML_HIDDEN int xmlUCSIsCatCc	(int code);
XML_HIDDEN int xmlUCSIsCatCf	(int code);
XML_HIDDEN int xmlUCSIsCatCo	(int code);
XML_HIDDEN int xmlUCSIsCatCs	(int code);
XML_HIDDEN int xmlUCSIsCatL	(int code);
XML_HIDDEN int xmlUCSIsCatLl	(int code);
XML_HIDDEN int xmlUCSIsCatLm	(int code);
XML_HIDDEN int xmlUCSIsCatLo	(int code);
XML_HIDDEN int xmlUCSIsCatLt	(int code);
XML_HIDDEN int xmlUCSIsCatLu	(int code);
XML_HIDDEN int xmlUCSIsCatM	(int code);
XML_HIDDEN int xmlUCSIsCatMc	(int code);
XML_HIDDEN int xmlUCSIsCatMe	(int code);
XML_HIDDEN int xmlUCSIsCatMn	(int code);
XML_HIDDEN int xmlUCSIsCatN	(int code);
XML_HIDDEN int xmlUCSIsCatNd	(int code);
XML_HIDDEN int xmlUCSIsCatNl	(int code);
XML_HIDDEN int xmlUCSIsCatNo	(int code);
XML_HIDDEN int xmlUCSIsCatP	(int code);
XML_HIDDEN int xmlUCSIsCatPc	(int code);
XML_HIDDEN int xmlUCSIsCatPd	(int code);
XML_HIDDEN int xmlUCSIsCatPe	(int code);
XML_HIDDEN int xmlUCSIsCatPf	(int code);
XML_HIDDEN int xmlUCSIsCatPi	(int code);
XML_HIDDEN int xmlUCSIsCatPo	(int code);
XML_HIDDEN int xmlUCSIsCatPs	(int code);
XML_HIDDEN int xmlUCSIsCatS	(int code);
XML_HIDDEN int xmlUCSIsCatSc	(int code);
XML_HIDDEN int xmlUCSIsCatSk	(int code);
XML_HIDDEN int xmlUCSIsCatSm	(int code);
XML_HIDDEN int xmlUCSIsCatSo	(int code);
XML_HIDDEN int xmlUCSIsCatZ	(int code);
XML_HIDDEN int xmlUCSIsCatZl	(int code);
XML_HIDDEN int xmlUCSIsCatZp	(int code);
XML_HIDDEN int xmlUCSIsCatZs	(int code);

#endif /* XML_UNICODE_H_PRIVATE__ */
